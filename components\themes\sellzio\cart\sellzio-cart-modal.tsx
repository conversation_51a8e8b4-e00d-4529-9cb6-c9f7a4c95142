"use client"

import React, { useState, useEffect } from 'react'
import { ArrowLeft } from 'lucide-react'
import { SellzioCartItem } from './sellzio-cart-item'
import { SellzioVoucherModal } from './sellzio-voucher-modal'
import { CartItem, Shop } from './types'
import './sellzio-cart-styles.css'

interface SellzioCartModalProps {
  isOpen: boolean
  onClose: () => void
  cartItems?: CartItem[]
}

// Sample data - akan diganti dengan data dari API
const sampleCartItems: CartItem[] = [
  {
    id: "1",
    productId: "prod-1",
    name: "Kaos Polos Premium Katun Combed 30s",
    price: 59000,
    originalPrice: 79000,
    image: "https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp",
    store: "Fashion Store",
    storeId: "store-1",
    quantity: 1,
    variant: "Put<PERSON>, L",
    selected: false,
    isLive: true
  },
  {
    id: "2",
    productId: "prod-2",
    name: "<PERSON><PERSON>ria Slim Fit Stretch Denim",
    price: 159000,
    originalPrice: 199000,
    image: "https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp",
    store: "Fashion Store",
    storeId: "store-1",
    quantity: 2,
    variant: "Blue Black, 32",
    selected: false,
    isLive: true
  },
  {
    id: "3",
    productId: "prod-3",
    name: "Sepatu Sneakers Casual Pria Wanita Unisex",
    price: 129000,
    image: "https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp",
    store: "Sport World",
    storeId: "store-2",
    quantity: 1,
    variant: "Hitam, 42",
    selected: false,
    badges: ["Star Plus"]
  }
]

export const SellzioCartModal: React.FC<SellzioCartModalProps> = ({
  isOpen,
  onClose,
  cartItems = []
}) => {
  const [shops, setShops] = useState<Shop[]>([])
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [selectedShops, setSelectedShops] = useState<string[]>([])
  const [selectAll, setSelectAll] = useState(false)
  const [isVoucherModalOpen, setIsVoucherModalOpen] = useState(false)
  const [appliedVouchers, setAppliedVouchers] = useState<any[]>([])
  const [isEditMode, setIsEditMode] = useState(false)

  // Group items by shop
  useEffect(() => {
    const items = cartItems.length > 0 ? cartItems : sampleCartItems
    const groupedShops: { [key: string]: Shop } = {}

    items.forEach((item: CartItem) => {
      if (!groupedShops[item.storeId]) {
        groupedShops[item.storeId] = {
          id: item.storeId,
          name: item.store,
          items: [],
          selected: false,
          isLive: item.isLive,
          badges: item.badges
        }
      }
      groupedShops[item.storeId].items.push(item)
    })

    setShops(Object.values(groupedShops))
  }, [cartItems])

  const handleItemSelect = (itemId: string, selected: boolean) => {
    if (selected) {
      setSelectedItems(prev => [...prev, itemId])
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId))
    }
  }

  const handleShopSelect = (shopId: string, selected: boolean) => {
    const shop = shops.find(s => s.id === shopId)
    if (!shop) return

    if (selected) {
      setSelectedShops(prev => [...prev, shopId])
      const shopItemIds = shop.items.map(item => item.id)
      setSelectedItems(prev => [...prev, ...shopItemIds])
    } else {
      setSelectedShops(prev => prev.filter(id => id !== shopId))
      const shopItemIds = shop.items.map(item => item.id)
      setSelectedItems(prev => prev.filter(id => !shopItemIds.includes(id)))
    }
  }

  const handleSelectAll = (selected: boolean) => {
    setSelectAll(selected)
    if (selected) {
      const allShopIds = shops.map(shop => shop.id)
      const allItemIds = shops.flatMap(shop => shop.items.map(item => item.id))
      setSelectedShops(allShopIds)
      setSelectedItems(allItemIds)
    } else {
      setSelectedShops([])
      setSelectedItems([])
    }
  }

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    setShops(prevShops => 
      prevShops.map(shop => ({
        ...shop,
        items: shop.items.map(item => 
          item.id === itemId ? { ...item, quantity: Math.max(1, newQuantity) } : item
        )
      }))
    )
  }

  const calculateTotal = () => {
    let total = 0
    shops.forEach(shop => {
      shop.items.forEach(item => {
        if (selectedItems.includes(item.id)) {
          total += item.price * item.quantity
        }
      })
    })
    return total
  }

  const getSelectedItemsCount = () => selectedItems.length

  if (!isOpen) return null

  return (
    <>
      <div className="sellzio-cart-modal-overlay" onClick={onClose}>
        <div className="sellzio-cart-modal" onClick={e => e.stopPropagation()}>
          {/* Header */}
          <div className="sellzio-cart-header">
            <div className="sellzio-cart-header-container">
              <h1>
                <span className="sellzio-cart-back-arrow" onClick={onClose}>
                  <ArrowLeft size={20} />
                </span>
                Keranjang Saya&nbsp;&nbsp;
                <span id="cart-count">({shops.reduce((total, shop) => total + shop.items.length, 0)})</span>
              </h1>
              <button 
                className="sellzio-cart-edit-btn"
                onClick={() => setIsEditMode(!isEditMode)}
              >
                {isEditMode ? 'Selesai' : 'Ubah'}
              </button>
            </div>
          </div>

          {/* Cart Items */}
          <div className="sellzio-cart-section">
            {shops.map(shop => (
              <div key={shop.id} className="sellzio-shop-group">
                <div className="sellzio-shop-header">
                  <div className="sellzio-shop-checkbox">
                    <input
                      type="checkbox"
                      checked={selectedShops.includes(shop.id)}
                      onChange={(e) => handleShopSelect(shop.id, e.target.checked)}
                    />
                  </div>
                  {shop.isLive && (
                    <div className="sellzio-live-badge">
                      <div className="sellzio-sound-wave">
                        <div className="sellzio-wave-bar"></div>
                        <div className="sellzio-wave-bar"></div>
                        <div className="sellzio-wave-bar"></div>
                      </div>
                      <span className="sellzio-live-text">LIVE</span>
                    </div>
                  )}
                  {shop.badges?.includes("Star Plus") && (
                    <div className="sellzio-star-label">
                      <div className="sellzio-star-primary">Star</div>
                      <div className="sellzio-star-secondary">Plus</div>
                    </div>
                  )}
                  <div className="sellzio-shop-name">{shop.name}</div>
                </div>

                {shop.items.map(item => (
                  <SellzioCartItem
                    key={item.id}
                    item={item}
                    selected={selectedItems.includes(item.id)}
                    onSelect={(selected: boolean) => handleItemSelect(item.id, selected)}
                    onQuantityChange={(quantity: number) => handleQuantityChange(item.id, quantity)}
                    isEditMode={isEditMode}
                  />
                ))}
              </div>
            ))}
          </div>

          {/* Footer */}
          <div className="sellzio-cart-footer">
            {/* Voucher Section */}
            <div className="sellzio-voucher-section">
              <div className="sellzio-section-title">
                <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.79 21L3 11.21v2c0 .45.54.67.85.35l8.79-8.79c.2-.2.2-.51 0-.71l-8.79-8.79C3.54 4.88 3 5.1 3 5.55v2L12.79 21z"/>
                </svg>
                <span>Voucher</span>
              </div>
              <div className="sellzio-voucher-badges">
                {appliedVouchers.map((voucher, index) => (
                  <div key={index} className={`sellzio-voucher-badge ${voucher.type}`}>
                    {voucher.name}
                    <span className="sellzio-voucher-badge-close">×</span>
                  </div>
                ))}
              </div>
              <div className="sellzio-section-action">
                <button 
                  className="sellzio-voucher-button"
                  onClick={() => setIsVoucherModalOpen(true)}
                >
                  Pilih Voucher
                </button>
              </div>
            </div>

            {/* Checkout Section */}
            <div className="sellzio-checkout-row">
              <div className="sellzio-select-all">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                />
                <label>Semua</label>
              </div>

              <div className="sellzio-checkout-section">
                <div className="sellzio-total-price">
                  <div className="sellzio-total-label">Total:</div>
                  <div className="sellzio-total-amount">
                    Rp{calculateTotal().toLocaleString('id-ID')}
                  </div>
                </div>
                <button 
                  className={`sellzio-checkout-btn ${getSelectedItemsCount() > 0 ? 'active' : ''}`}
                  disabled={getSelectedItemsCount() === 0}
                >
                  Checkout ({getSelectedItemsCount()})
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Voucher Modal */}
      <SellzioVoucherModal
        isOpen={isVoucherModalOpen}
        onClose={() => setIsVoucherModalOpen(false)}
        onApplyVouchers={setAppliedVouchers}
        appliedVouchers={appliedVouchers}
      />
    </>
  )
}
