"use client"

import { useState } from "react"
import Image from "next/image"
import {
  MallBadge,
  StarBadge,
  TermurahDiTokoBadge,
  CodBadge,
  RatingStars,
  ShopeeStarIcon,
  ShippingBadge,
  LiveBadge,
} from "./badges"
import { FlashSale } from "./flash-sale"
import { ImageSlider } from "./image-slider"
import { VideoCard } from "./video-card"

export type BadgeType = "mall" | "star" | "termurah" | "none"
export type CardType = "standard" | "flash-sale" | "video" | "image-slider"

export interface ProductCardProps {
  type: CardType
  name: string
  price: string
  originalPrice?: string
  discount?: string
  image: string
  images?: { src: string; alt: string }[]
  badgeType?: BadgeType
  rating: number
  sold: number
  shipping?: string
  hasCod?: boolean
  videoThumbnail?: string
  videoSrc?: string
  isLive?: boolean
  flashSale?: {
    endTime: Date
    remaining: number
    total: number
  }
}

export const ProductCard = ({
  type,
  name,
  price,
  originalPrice,
  discount,
  image,
  images,
  badgeType = "none",
  rating,
  sold,
  shipping = "Pengiriman Instan",
  hasCod = false,
  videoThumbnail,
  videoSrc,
  isLive,
  flashSale,
}: ProductCardProps) => {
  const [isLoaded, setIsLoaded] = useState(false)

  // Render video card
  if (type === "video" && videoThumbnail) {
    return (
      <VideoCard
        thumbnailSrc={videoThumbnail}
        videoSrc={videoSrc}
        isLive={isLive}
        productImage={image}
        productName={name}
        rating={rating}
        sold={sold}
        price={price}
        originalPrice={originalPrice}
      />
    )
  }

  // Render image slider card
  if (type === "image-slider" && images && images.length > 0) {
    return (
      <div className="bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 cursor-pointer">
        <ImageSlider images={images} />
        <div className="p-2 pb-3">
          <h3 className="text-xs text-gray-800 line-clamp-2 h-10 mb-1.5">
            {badgeType === "mall" && <MallBadge />}
            {badgeType === "star" && <StarBadge />}
            {name}
          </h3>

          {badgeType === "termurah" && <TermurahDiTokoBadge />}

          <div className="flex items-center text-xs text-gray-500 mb-1.5 relative">
            <RatingStars rating={rating} />
            <div className="mx-1 w-[1px] h-2 bg-gray-300"></div>
            <span>Terjual {sold}</span>
            {hasCod && <CodBadge />}
          </div>

          <ShippingBadge text={shipping} />

          <div className="flex items-center">
            <div className="text-sm font-bold text-[#ee4d2d]">{price}</div>
            {originalPrice && (
              <div className="ml-1 text-xs text-gray-400 line-through overflow-hidden text-ellipsis max-w-[40%]">
                {originalPrice}
              </div>
            )}
            {discount && (
              <div className="ml-1 text-[8px] bg-[#ffeee8] text-[#ee4d2d] px-1 py-0.5 rounded">{discount}</div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Render standard or flash-sale card
  return (
    <div className="bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 cursor-pointer">
      <div className="relative pt-[100%] overflow-hidden bg-white border-b border-[#f2f2f2]">
        <Image
          src={image || "/placeholder.svg"}
          alt={name}
          fill
          className={`object-contain p-0.5 transition-opacity duration-300 ${isLoaded ? "opacity-100" : "opacity-0"}`}
          onLoad={() => setIsLoaded(true)}
          sizes="(max-width: 768px) 50vw, 33vw"
        />
        {!isLoaded && <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>}
        {hasCod && <CodBadge />}
        {/* Badge LIVE dihapus sesuai permintaan user */}
      </div>

      <div className="p-2 pb-3">
        <h3 className="text-xs text-gray-800 line-clamp-2 h-10 mb-1.5">
          {badgeType === "mall" && <MallBadge />}
          {badgeType === "star" && <StarBadge />}
          {badgeType === "none" && <ShopeeStarIcon />}
          {name}
        </h3>

        {badgeType === "termurah" && <TermurahDiTokoBadge />}

        {type === "flash-sale" && flashSale && (
          <FlashSale
            endTime={flashSale.endTime}
            originalPrice={originalPrice || ""}
            currentPrice={price}
            remaining={flashSale.remaining}
            total={flashSale.total}
          />
        )}

        <div className="flex items-center text-xs text-gray-500 mb-1.5 relative">
          <RatingStars rating={rating} />
          <div className="mx-1 w-[1px] h-2 bg-gray-300"></div>
          <span>Terjual {sold}</span>
        </div>

        <ShippingBadge text={shipping} />

        {type !== "flash-sale" && (
          <div className="flex items-center">
            <div className="text-sm font-bold text-[#ee4d2d]">{price}</div>
            {originalPrice && (
              <div className="ml-1 text-xs text-gray-400 line-through overflow-hidden text-ellipsis max-w-[40%]">
                {originalPrice}
              </div>
            )}
            {discount && (
              <div className="ml-1 text-[8px] bg-[#ffeee8] text-[#ee4d2d] px-1 py-0.5 rounded">{discount}</div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
