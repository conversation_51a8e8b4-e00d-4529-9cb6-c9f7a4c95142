"use client"

import { useState, useEffect } from 'react'

interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  originalPrice?: number
  image: string
  store: string
  storeId: string
  quantity: number
  variant?: string
  selected?: boolean
  isLive?: boolean
  badges?: string[]
}

interface UseCartReturn {
  cartItems: CartItem[]
  cartCount: number
  isLoading: boolean
  addToCart: (item: Omit<CartItem, 'id' | 'quantity'>) => Promise<void>
  removeFromCart: (itemId: string) => Promise<void>
  updateQuantity: (itemId: string, quantity: number) => Promise<void>
  clearCart: () => Promise<void>
  refreshCart: () => Promise<void>
}

export const useCart = (): UseCartReturn => {
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Fetch cart items from API
  const fetchCartItems = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/cart')
      if (response.ok) {
        const items = await response.json()
        setCartItems(items)
      }
    } catch (error) {
      console.error('Error fetching cart items:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Add item to cart
  const addToCart = async (item: Omit<CartItem, 'id' | 'quantity'>) => {
    try {
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...item,
          quantity: 1
        }),
      })

      if (response.ok) {
        await fetchCartItems() // Refresh cart after adding
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      throw error
    }
  }

  // Remove item from cart
  const removeFromCart = async (itemId: string) => {
    try {
      const response = await fetch(`/api/cart/${itemId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setCartItems(prev => prev.filter(item => item.id !== itemId))
      }
    } catch (error) {
      console.error('Error removing from cart:', error)
      throw error
    }
  }

  // Update item quantity
  const updateQuantity = async (itemId: string, quantity: number) => {
    try {
      const response = await fetch(`/api/cart/${itemId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ quantity }),
      })

      if (response.ok) {
        setCartItems(prev => 
          prev.map(item => 
            item.id === itemId ? { ...item, quantity } : item
          )
        )
      }
    } catch (error) {
      console.error('Error updating quantity:', error)
      throw error
    }
  }

  // Clear entire cart
  const clearCart = async () => {
    try {
      const response = await fetch('/api/cart', {
        method: 'DELETE',
      })

      if (response.ok) {
        setCartItems([])
      }
    } catch (error) {
      console.error('Error clearing cart:', error)
      throw error
    }
  }

  // Refresh cart data
  const refreshCart = async () => {
    await fetchCartItems()
  }

  // Calculate cart count
  const cartCount = cartItems.reduce((total, item) => total + item.quantity, 0)

  // Load cart items on mount
  useEffect(() => {
    fetchCartItems()
  }, [])

  return {
    cartItems,
    cartCount,
    isLoading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    refreshCart
  }
}
