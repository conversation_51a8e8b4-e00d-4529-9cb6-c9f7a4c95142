"use client"

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { SellzioCheckoutPage } from '@/components/themes/sellzio/checkout'
import { CartItem } from '@/components/themes/sellzio/cart/types'

export default function CheckoutPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [checkoutItems, setCheckoutItems] = useState<CartItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Get selected items from URL params or localStorage
    const selectedItemsParam = searchParams.get('items')
    
    if (selectedItemsParam) {
      try {
        const items = JSON.parse(decodeURIComponent(selectedItemsParam))
        setCheckoutItems(items)
      } catch (error) {
        console.error('Error parsing checkout items:', error)
        // Fallback to sample data
        setCheckoutItems(getSampleCheckoutItems())
      }
    } else {
      // Fallback to sample data
      setCheckoutItems(getSampleCheckoutItems())
    }
    
    setIsLoading(false)
  }, [searchParams])

  const getSampleCheckoutItems = (): CartItem[] => [
    {
      id: "1",
      productId: "prod-1",
      name: "Sepatu Running Premium Ultra Boost",
      price: 599000,
      originalPrice: 799000,
      image: "https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp",
      store: "Sepatu Official Store",
      storeId: "store-1",
      quantity: 1,
      variant: "Warna: Hitam, Ukuran: 42",
      selected: true
    },
    {
      id: "2",
      productId: "prod-2",
      name: "T-Shirt Katun Premium",
      price: 199000,
      image: "https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp",
      store: "Fashion Store",
      storeId: "store-2",
      quantity: 2,
      variant: "Warna: Putih, Ukuran: L",
      selected: true
    },
    {
      id: "3",
      productId: "prod-3",
      name: "Tas Ransel Waterproof",
      price: 349000,
      originalPrice: 450000,
      image: "https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp",
      store: "Outdoor Gear Mall",
      storeId: "store-3",
      quantity: 1,
      variant: "Warna: Navy Blue",
      selected: true
    }
  ]

  const handleBackToCart = () => {
    router.push('/sellzio')
  }

  const handlePlaceOrder = (orderData: any) => {
    console.log('Order placed:', orderData)
    // Here you would typically send the order to your API
    // For now, we'll just show a success message and redirect
    alert('Pesanan berhasil dibuat!')
    router.push('/sellzio/order-success')
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat checkout...</p>
        </div>
      </div>
    )
  }

  return (
    <SellzioCheckoutPage
      items={checkoutItems}
      onBack={handleBackToCart}
      onPlaceOrder={handlePlaceOrder}
    />
  )
}
