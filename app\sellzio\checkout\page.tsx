"use client"

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { SellzioCheckoutPage } from '@/components/themes/sellzio/checkout'
import { SellzioErrorBoundary } from '@/components/themes/sellzio/checkout/sellzio-error-boundary'
import { CartItem } from '@/components/themes/sellzio/cart/types'

// Define sample data function first
const getSampleCheckoutItems = (): CartItem[] => [
  {
    id: "1",
    productId: "prod-1",
    name: "Sepatu Running Premium Ultra Boost",
    price: 599000,
    originalPrice: 799000,
    image: "https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp",
    store: "Sepatu Official Store",
    storeId: "store-1",
    quantity: 1,
    variant: "Warna: Hitam, Ukuran: 42",
    selected: true
  },
  {
    id: "2",
    productId: "prod-2",
    name: "T-Shirt Katun Premium",
    price: 199000,
    image: "https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp",
    store: "Fashion Store",
    storeId: "store-2",
    quantity: 2,
    variant: "Warna: Putih, Ukuran: L",
    selected: true
  },
  {
    id: "3",
    productId: "prod-3",
    name: "Tas Ransel Waterproof",
    price: 349000,
    originalPrice: 450000,
    image: "https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp",
    store: "Outdoor Gear Mall",
    storeId: "store-3",
    quantity: 1,
    variant: "Warna: Navy Blue",
    selected: true
  }
]

export default function CheckoutPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [checkoutItems, setCheckoutItems] = useState<CartItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadData = () => {
      try {
        // Always start with sample data to ensure something renders
        let items = getSampleCheckoutItems()

        // Try to get from URL params
        const selectedItemsParam = searchParams.get('items')
        if (selectedItemsParam) {
          try {
            const urlItems = JSON.parse(decodeURIComponent(selectedItemsParam))
            if (Array.isArray(urlItems) && urlItems.length > 0) {
              items = urlItems
              console.log('Loaded items from URL:', items.length)
            }
          } catch (e) {
            console.warn('Failed to parse URL items:', e)
          }
        } else {
          console.log('No URL params, using sample data')
        }

        setCheckoutItems(items)
        console.log('Checkout items set:', items.length)
      } catch (error) {
        console.error('Error loading checkout:', error)
        setCheckoutItems(getSampleCheckoutItems())
      }

      // Short delay to ensure proper rendering
      setTimeout(() => {
        setIsLoading(false)
        console.log('Loading complete')
      }, 100)
    }

    loadData()
  }, [searchParams])



  const handleBackToCart = () => {
    router.push('/sellzio')
  }

  const handlePlaceOrder = (orderData: any) => {
    console.log('Order placed:', orderData)
    // Here you would typically send the order to your API
    // For now, we'll just show a success message and redirect
    alert('Pesanan berhasil dibuat!')
    router.push('/sellzio/order-success')
  }

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          textAlign: 'center',
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '3px solid #f3f3f3',
            borderTop: '3px solid #ee4d2d',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#666', fontWeight: '500' }}>Memuat checkout...</p>
        </div>
      </div>
    )
  }

  return (
    <SellzioErrorBoundary>
      <SellzioCheckoutPage
        items={checkoutItems}
        onBack={handleBackToCart}
        onPlaceOrder={handlePlaceOrder}
      />
    </SellzioErrorBoundary>
  )
}
