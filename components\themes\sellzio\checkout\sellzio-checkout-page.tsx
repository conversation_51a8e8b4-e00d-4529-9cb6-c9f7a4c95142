"use client"

import React, { useState } from 'react'
import { ArrowLeft } from 'lucide-react'
import { CartItem } from '../cart/types'
import { SellzioCheckoutHeader } from './sellzio-checkout-header'
import { SellzioAddressSection } from './sellzio-address-section'
import { SellzioOrderDetails } from './sellzio-order-details'
import { SellzioShippingSection } from './sellzio-shipping-section'
import { SellzioPaymentSection } from './sellzio-payment-section'
import { SellzioVoucherSection } from './sellzio-voucher-section'
import { SellzioPaymentSummary } from './sellzio-payment-summary'
import { SellzioCheckoutFooter } from './sellzio-checkout-footer'
import './sellzio-checkout-styles.css'

interface Address {
  name: string
  phone: string
  address: string
}

interface ShippingOption {
  id: string
  name: string
  price: number
  originalPrice?: number
  estimate: string
  isFree?: boolean
  isSelected?: boolean
}

interface PaymentMethod {
  id: string
  name: string
  type: string
  isSelected?: boolean
}

interface SellzioCheckoutPageProps {
  items: CartItem[]
  onBack: () => void
  onPlaceOrder: (orderData: any) => void
}

export const SellzioCheckoutPage: React.FC<SellzioCheckoutPageProps> = ({
  items,
  onBack,
  onPlaceOrder
}) => {
  const [address] = useState<Address>({
    name: "Budi Santoso",
    phone: "0812-3456-7890",
    address: "Jl. Merdeka No. 123, RT 05/RW 10, Kelurahan Sejahtera, Kecamatan Bahagia, Kota Jakarta Selatan, DKI Jakarta, 12345"
  })

  const [shippingOptions] = useState<ShippingOption[]>([
    {
      id: "reguler",
      name: "Reguler",
      price: 0,
      originalPrice: 17000,
      estimate: "20 - 23 Mar",
      isFree: true,
      isSelected: true
    }
  ])

  const [paymentMethod] = useState<PaymentMethod>({
    id: "bca",
    name: "Transfer Bank - Bank BCA",
    type: "bank_transfer",
    isSelected: true
  })

  const [appliedVouchers, setAppliedVouchers] = useState<any[]>([
    {
      id: "discount50k",
      name: "Diskon Rp50.000",
      amount: 50000,
      type: "discount"
    }
  ])

  // Calculate totals
  const subtotal = items.reduce((total, item) => total + (item.price * item.quantity), 0)
  const shippingCost = shippingOptions.find(opt => opt.isSelected)?.price || 0
  const voucherDiscount = appliedVouchers.reduce((total, voucher) => total + voucher.amount, 0)
  const totalPayment = subtotal + shippingCost - voucherDiscount

  const handleEditAddress = () => {
    console.log('Edit address clicked')
    // Implement address editing
  }

  const handleEditShipping = () => {
    console.log('Edit shipping clicked')
    // Implement shipping options
  }

  const handleEditPayment = () => {
    console.log('Edit payment clicked')
    // Implement payment method selection
  }

  const handleSelectVoucher = () => {
    console.log('Select voucher clicked')
    // Implement voucher selection
  }

  const handlePlaceOrder = () => {
    const orderData = {
      items,
      address,
      shipping: shippingOptions.find(opt => opt.isSelected),
      payment: paymentMethod,
      vouchers: appliedVouchers,
      subtotal,
      shippingCost,
      voucherDiscount,
      total: totalPayment
    }
    
    onPlaceOrder(orderData)
  }

  return (
    <div className="sellzio-checkout-page">
      <SellzioCheckoutHeader onBack={onBack} />
      
      <div className="sellzio-checkout-container">
        <SellzioAddressSection 
          address={address}
          onEdit={handleEditAddress}
        />
        
        <SellzioOrderDetails items={items} />
        
        <SellzioShippingSection 
          options={shippingOptions}
          onEdit={handleEditShipping}
        />
        
        <SellzioPaymentSection 
          method={paymentMethod}
          onEdit={handleEditPayment}
        />
        
        <SellzioVoucherSection 
          vouchers={appliedVouchers}
          onSelect={handleSelectVoucher}
        />
        
        <SellzioPaymentSummary 
          subtotal={subtotal}
          shippingCost={shippingCost}
          voucherDiscount={voucherDiscount}
          total={totalPayment}
        />
      </div>
      
      <SellzioCheckoutFooter 
        total={totalPayment}
        onPlaceOrder={handlePlaceOrder}
      />
    </div>
  )
}
