/* Sellzio Checkout Styles */

/* Reset and Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Page Layout */
.sellzio-checkout-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
}

/* Header */
.sellzio-checkout-header {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #fff;
  padding: 15px 0;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 800px) {
  .sellzio-checkout-header-container {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    width: 100%;
  }
}

.sellzio-checkout-header-container {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  width: 100%;
  padding: 0 15px;
}

.sellzio-checkout-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0;
}

.sellzio-checkout-back-btn {
  position: absolute;
  left: 15px;
  border: none;
  background: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Container */
.sellzio-checkout-container {
  max-width: 800px;
  margin: 3px auto 0;
  padding: 15px;
  padding-bottom: 120px; /* Space for fixed footer */
  min-height: calc(100vh - 80px); /* Ensure full height minus header */
}

/* Section */
.sellzio-checkout-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.sellzio-section-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sellzio-edit-btn {
  color: #ee4d2d;
  font-size: 0.9rem;
  font-weight: normal;
  background: none;
  border: none;
  cursor: pointer;
}

/* Address Section */
.sellzio-address-info {
  margin-bottom: 6px;
  font-size: 14px;
}

.sellzio-address-name-phone {
  margin-bottom: 4px;
}

.sellzio-address-name {
  font-weight: bold;
}

.sellzio-address-phone {
  margin-left: 10px;
  color: #666;
}

.sellzio-address-text {
  color: #666;
  margin-top: 4px;
}

/* Product Section */
.sellzio-product-container {
  margin-bottom: 10px;
}

.sellzio-store-group {
  margin-bottom: 15px;
}

.sellzio-store-group:last-child {
  margin-bottom: 0;
}

.sellzio-product-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  flex-direction: column;
}

.sellzio-product-item:last-child {
  border-bottom: none;
}

.sellzio-product-shop {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.sellzio-shop-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.sellzio-product-content {
  display: flex;
  margin-top: 5px;
}

.sellzio-product-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  object-fit: cover;
  margin-right: 15px;
}

.sellzio-product-details {
  flex: 1;
}

.sellzio-product-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
}

.sellzio-product-variant {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 5px;
}

.sellzio-product-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
}

.sellzio-quantity-display {
  font-size: 0.9rem;
  color: #666;
  margin-left: auto;
}

.sellzio-quantity {
  font-weight: bold;
  color: #333;
}

.sellzio-product-price {
  color: #ee4d2d;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sellzio-price-original {
  color: #999;
  text-decoration: line-through;
  font-weight: normal;
  font-size: 0.85em;
}

/* Shipping Section */
.sellzio-shipping-option-selected {
  position: relative;
  border: 1px solid #00BFA5;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  cursor: pointer;
  background-color: #fff;
}

.sellzio-free-shipping-badge {
  position: absolute;
  top: -10px;
  left: 15px;
  background-color: #00BFA5;
  color: white;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  z-index: 1;
}

.sellzio-shipping-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  align-items: center;
}

.sellzio-shipping-name {
  font-weight: bold;
  font-size: 14px;
}

.sellzio-shipping-price-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sellzio-shipping-original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 14px;
  margin-right: 8px;
}

.sellzio-shipping-price {
  font-weight: bold;
  font-size: 16px;
}

.sellzio-shipping-check {
  color: #00BFA5;
  margin-left: 5px;
}

.sellzio-shipping-estimate {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  color: #00BFA5;
}

.sellzio-truck-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.sellzio-estimate-text {
  font-weight: 500;
  color: #00BFA5;
  font-size: 12px;
}

.sellzio-shipping-guarantee {
  color: #666;
  font-size: 12px;
  margin-bottom: -10px;
}

/* Payment Section */
.sellzio-payment-section {
  cursor: pointer;
}

.sellzio-payment-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sellzio-payment-left {
  display: flex;
  align-items: center;
}

.sellzio-payment-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.sellzio-payment-label {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
}

.sellzio-payment-right {
  display: flex;
  align-items: center;
}

.sellzio-payment-method {
  margin-right: 10px;
  color: #666;
  font-size: 14px;
}

.sellzio-payment-arrow {
  color: #999;
  font-size: 30px;
}

/* Voucher Section */
.sellzio-voucher-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  cursor: pointer;
}

.sellzio-voucher-label {
  display: flex;
  align-items: center;
}

.sellzio-voucher-icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.sellzio-voucher-applied {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sellzio-voucher-count {
  color: #00BFA5;
  font-size: 14px;
}

/* Payment Summary */
.sellzio-cost-breakdown {
  margin-top: 10px;
}

.sellzio-cost-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.sellzio-discount {
  color: #00BFA5;
}

.sellzio-total-cost {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 1.1rem;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.sellzio-total-price {
  color: #ee4d2d;
}

/* Footer */
.sellzio-checkout-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
  z-index: 50;
  border-radius: 8px;
}

.sellzio-checkout-footer-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sellzio-order-total {
  font-size: 1.1rem;
  font-weight: bold;
  color: #ee4d2d;
}

.sellzio-order-btn {
  background-color: #ee4d2d;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.sellzio-order-btn:hover {
  background-color: #d63c1e;
}

.sellzio-order-btn:disabled {
  background-color: #aaa;
  cursor: not-allowed;
}

/* Toast Notification */
.sellzio-toast {
  position: fixed;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0,0,0,0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 1000;
  font-size: 14px;
  max-width: 300px;
  text-align: center;
}

.sellzio-toast-success {
  background-color: rgba(34, 197, 94, 0.9);
}

.sellzio-toast-error {
  background-color: rgba(239, 68, 68, 0.9);
}

.sellzio-toast-info {
  background-color: rgba(0,0,0,0.7);
}

/* Responsive */
@media (max-width: 480px) {
  .sellzio-checkout-container {
    padding: 10px;
  }

  .sellzio-checkout-section {
    padding: 12px;
  }

  .sellzio-product-image {
    width: 60px;
    height: 60px;
  }

  .sellzio-checkout-footer-content {
    padding: 10px;
  }

  .sellzio-order-btn {
    padding: 10px 20px;
  }

  .sellzio-toast {
    max-width: 250px;
    font-size: 13px;
  }
}
