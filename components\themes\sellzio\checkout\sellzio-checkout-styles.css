/* Sellzio Checkout Styles */

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Page Layout - High Specificity */
.sellzio-checkout-page {
  min-height: 100vh !important;
  background-color: #f5f5f5 !important;
  color: #333 !important;
  line-height: 1.6 !important;
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif !important;
  width: 100% !important;
  overflow-x: hidden !important;
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure proper box-sizing for checkout page */
.sellzio-checkout-page *,
.sellzio-checkout-page *::before,
.sellzio-checkout-page *::after {
  box-sizing: border-box !important;
}

/* Header */
.sellzio-checkout-header {
  position: sticky !important;
  top: 0 !important;
  width: 100% !important;
  background-color: #fff !important;
  padding: 15px 0 !important;
  text-align: center !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  z-index: 100 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

@media (min-width: 800px) {
  .sellzio-checkout-header-container {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    width: 100%;
  }
}

.sellzio-checkout-header-container {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  width: 100%;
  padding: 0 15px;
}

.sellzio-checkout-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0;
}

.sellzio-checkout-back-btn {
  position: absolute;
  left: 15px;
  border: none;
  background: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Container */
.sellzio-checkout-container {
  max-width: 800px !important;
  margin: 3px auto 0 !important;
  padding: 15px !important;
  padding-bottom: 120px !important; /* Space for fixed footer */
  min-height: calc(100vh - 80px) !important; /* Ensure full height minus header */
  width: 100% !important;
  flex: 1 !important;
}

/* Section */
.sellzio-checkout-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.sellzio-section-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sellzio-edit-btn {
  color: #ee4d2d;
  font-size: 0.9rem;
  font-weight: normal;
  background: none;
  border: none;
  cursor: pointer;
}

/* Address Section */
.sellzio-address-info {
  margin-bottom: 6px;
  font-size: 14px;
}

.sellzio-address-name-phone {
  margin-bottom: 4px;
}

.sellzio-address-name {
  font-weight: bold;
}

.sellzio-address-phone {
  margin-left: 10px;
  color: #666;
}

.sellzio-address-text {
  color: #666;
  margin-top: 4px;
}

/* Product Section */
.sellzio-product-container {
  margin-bottom: 10px;
}

.sellzio-store-group {
  margin-bottom: 15px;
}

.sellzio-store-group:last-child {
  margin-bottom: 0;
}

.sellzio-product-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  flex-direction: column;
}

.sellzio-product-item:last-child {
  border-bottom: none;
}

.sellzio-product-shop {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.sellzio-shop-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.sellzio-product-content {
  display: flex;
  margin-top: 5px;
}

.sellzio-product-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  object-fit: cover;
  margin-right: 15px;
}

.sellzio-product-details {
  flex: 1;
}

.sellzio-product-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
}

.sellzio-product-variant {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 5px;
}

.sellzio-product-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
}

.sellzio-quantity-display {
  font-size: 0.9rem;
  color: #666;
  margin-left: auto;
}

.sellzio-quantity {
  font-weight: bold;
  color: #333;
}

.sellzio-product-price {
  color: #ee4d2d;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sellzio-price-original {
  color: #999;
  text-decoration: line-through;
  font-weight: normal;
  font-size: 0.85em;
}

/* Shipping Section */
.sellzio-shipping-option-selected {
  position: relative;
  border: 1px solid #00BFA5;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  cursor: pointer;
  background-color: #fff;
}

.sellzio-free-shipping-badge {
  position: absolute;
  top: -10px;
  left: 15px;
  background-color: #00BFA5;
  color: white;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  z-index: 1;
}

.sellzio-shipping-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  align-items: center;
}

.sellzio-shipping-name {
  font-weight: bold;
  font-size: 14px;
}

.sellzio-shipping-price-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sellzio-shipping-original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 14px;
  margin-right: 8px;
}

.sellzio-shipping-price {
  font-weight: bold;
  font-size: 16px;
}

.sellzio-shipping-check {
  color: #00BFA5;
  margin-left: 5px;
}

.sellzio-shipping-estimate {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  color: #00BFA5;
}

.sellzio-truck-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.sellzio-estimate-text {
  font-weight: 500;
  color: #00BFA5;
  font-size: 12px;
}

.sellzio-shipping-guarantee {
  color: #666;
  font-size: 12px;
  margin-bottom: -10px;
}

/* Payment Section */
.sellzio-payment-section {
  cursor: pointer;
}

.sellzio-payment-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sellzio-payment-left {
  display: flex;
  align-items: center;
}

.sellzio-payment-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.sellzio-payment-label {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
}

.sellzio-payment-right {
  display: flex;
  align-items: center;
}

.sellzio-payment-method {
  margin-right: 10px;
  color: #666;
  font-size: 14px;
}

.sellzio-payment-arrow {
  color: #999;
  font-size: 30px;
}

/* Voucher Section */
.sellzio-voucher-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  cursor: pointer;
}

.sellzio-voucher-label {
  display: flex;
  align-items: center;
}

.sellzio-voucher-icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.sellzio-voucher-applied {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sellzio-voucher-count {
  color: #00BFA5;
  font-size: 14px;
}

/* Payment Summary */
.sellzio-cost-breakdown {
  margin-top: 10px;
}

.sellzio-cost-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.sellzio-discount {
  color: #00BFA5;
}

.sellzio-total-cost {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 1.1rem;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.sellzio-total-price {
  color: #ee4d2d;
}

/* Footer */
.sellzio-checkout-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
  z-index: 50;
  border-radius: 8px;
}

.sellzio-checkout-footer-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sellzio-order-total {
  font-size: 1.1rem;
  font-weight: bold;
  color: #ee4d2d;
}

.sellzio-order-btn {
  background-color: #ee4d2d;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.sellzio-order-btn:hover {
  background-color: #d63c1e;
}

.sellzio-order-btn:disabled {
  background-color: #aaa;
  cursor: not-allowed;
}

/* Modal Styles */
.sellzio-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.sellzio-modal {
  background-color: #fff;
  border-radius: 8px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.sellzio-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.sellzio-modal-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: bold;
}

.sellzio-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  color: #666;
}

.sellzio-modal-content {
  padding: 20px;
}

.sellzio-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
}

/* Form Styles */
.sellzio-form-group {
  margin-bottom: 20px;
}

.sellzio-form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.sellzio-form-input,
.sellzio-form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.sellzio-form-input:focus,
.sellzio-form-textarea:focus {
  outline: none;
  border-color: #ee4d2d;
}

.sellzio-form-input.error,
.sellzio-form-textarea.error {
  border-color: #ef4444;
}

.sellzio-form-error {
  display: block;
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

.sellzio-form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* Button Styles */
.sellzio-btn-primary {
  background-color: #ee4d2d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.sellzio-btn-primary:hover {
  background-color: #d63c1e;
}

.sellzio-btn-secondary {
  background-color: #fff;
  color: #666;
  border: 1px solid #ddd;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.sellzio-btn-secondary:hover {
  background-color: #f5f5f5;
}

/* Toast Notification */
.sellzio-toast {
  position: fixed;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0,0,0,0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 1000;
  font-size: 14px;
  max-width: 300px;
  text-align: center;
}

.sellzio-toast-success {
  background-color: rgba(34, 197, 94, 0.9);
}

.sellzio-toast-error {
  background-color: rgba(239, 68, 68, 0.9);
}

.sellzio-toast-info {
  background-color: rgba(0,0,0,0.7);
}

/* Shipping Options Modal */
.sellzio-shipping-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sellzio-shipping-option-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.sellzio-shipping-option-item:hover {
  border-color: #ee4d2d;
}

.sellzio-shipping-option-item.selected {
  border-color: #ee4d2d;
  background-color: #fef7f0;
}

.sellzio-shipping-option-radio {
  margin-right: 15px;
}

.sellzio-shipping-option-radio input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: #ee4d2d;
}

.sellzio-shipping-option-details {
  flex: 1;
}

.sellzio-shipping-option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.sellzio-shipping-option-name {
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sellzio-free-badge {
  background-color: #00BFA5;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
}

.sellzio-shipping-option-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sellzio-original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 12px;
}

.sellzio-current-price {
  font-weight: bold;
  color: #ee4d2d;
}

.sellzio-shipping-option-estimate {
  color: #666;
  font-size: 12px;
}

/* Payment Methods Modal */
.sellzio-payment-groups {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sellzio-payment-group-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.sellzio-payment-method-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
}

.sellzio-payment-method-item:hover {
  border-color: #ee4d2d;
}

.sellzio-payment-method-item.selected {
  border-color: #ee4d2d;
  background-color: #fef7f0;
}

.sellzio-payment-method-radio {
  margin-right: 12px;
}

.sellzio-payment-method-radio input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: #ee4d2d;
}

.sellzio-payment-method-icon {
  margin-right: 12px;
  font-size: 20px;
}

.sellzio-payment-method-name {
  font-weight: 500;
}

/* Responsive */
@media (max-width: 480px) {
  .sellzio-checkout-container {
    padding: 10px;
  }

  .sellzio-checkout-section {
    padding: 12px;
  }

  .sellzio-product-image {
    width: 60px;
    height: 60px;
  }

  .sellzio-checkout-footer-content {
    padding: 10px;
  }

  .sellzio-order-btn {
    padding: 10px 20px;
  }

  .sellzio-toast {
    max-width: 250px;
    font-size: 13px;
  }

  .sellzio-modal {
    margin: 10px;
    max-height: 95vh;
  }

  .sellzio-modal-header,
  .sellzio-modal-content,
  .sellzio-modal-footer {
    padding: 15px;
  }

  .sellzio-shipping-option-item,
  .sellzio-payment-method-item {
    padding: 10px;
  }

  .sellzio-modal-footer {
    flex-direction: column;
  }

  .sellzio-btn-primary,
  .sellzio-btn-secondary {
    width: 100%;
    margin-bottom: 10px;
  }

  .sellzio-btn-primary {
    order: 1;
  }

  .sellzio-btn-secondary {
    order: 2;
    margin-bottom: 0;
  }
}
